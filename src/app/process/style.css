.CategoryBox_content__detail {
  margin-top: 2.7rem;
  display: flex;
  width: 50%;
  flex-direction: column;
  align-items: center;
}
.CategoryBox_content__detail:hover .CategoryBox_logo__icon--fill {
  fill: rgb(239, 68, 68);
}

.CategoryBox_content__detail:hover .CategoryBox_logo__icon--stroke {
  stroke: rgb(239, 68, 68);
}
.CategoryBox_content__detail:hover .CategoryBox_detail__content__title {
  color: rgb(239, 68, 68);
}
.CategoryBox_content__detail:hover .CategoryBox_detail__logo {
  box-shadow: 0 4px 32px rgba(0, 0, 0, 0.1);
  border-style: none;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255);
}

.link-img-service {
  box-sizing: border-box;
  display: inline-block;
  overflow: hidden;
  width: initial;
  height: initial;
  background: none;
  opacity: 1;
  border: 0px;
  margin: 0px;
  padding: 0px;
  position: relative;
  max-width: 100%;
}
