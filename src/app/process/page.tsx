"use client";
import { useState } from "react";
import Contact from "@/app/components/contact";

export default function Process() {
  const [activeStep, setActiveStep] = useState(1);

  const processSteps = [
    {
      id: 1,
      title: "Obtain customer information:  ",
      subtitle: "",
      description:
        "Our staff will ask about your account requirements, including the products you want to promote, the currency and time zone, and more.",
      icon: "💬",
      color: "from-blue-500 to-purple-600",
      details: [],
    },
    {
      id: 2,
      title: "Provide account",
      subtitle: "",
      description:
        "Provide for customer some innitial materials such as vps/adspower/gologin (clean proxy), strong profile, strong fanpage, strong account.",
      details: [],
      icon: "⚙️",
      color: "from-purple-500 to-pink-600",
    },
    {
      id: 3,
      title: "Run Free test",
      subtitle: "",
      description: "Customer have time for free testing account. ",
      details: [],
      icon: "🧪",
      color: "from-pink-500 to-red-600",
    },
    {
      id: 4,
      title: "Payment",
      subtitle: "",
      description:
        "After the test is successful and the customer wants to spend more, they top up their account with us.",
      details: [],
      icon: "🤝",
      color: "from-green-500 to-blue-600",
    },
  ];

  const benefits = [
    {
      icon: "🎯",
      title: "A quick and simple process that saves time for both parties",
      description: "",
    },
    {
      icon: "⚡",
      title: "A process that optimizes customer risks",
      description: "",
    },
    {
      icon: "🛡️",
      title: "A complete, transparent and clear process",
      description: "",
    },
  ];

  return (
    <div className="overflow-x-hidden">
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
        <div className="container mx-auto px-6 text-center">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-4xl md:text-6xl font-bold text-white mb-6 leading-tight">
              Our{" "}
              <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                Process
              </span>
            </h1>
            <p className="text-xl md:text-2xl text-gray-200 mb-8 max-w-3xl mx-auto">
              A streamlined 4-step process designed to get your Facebook
              advertising campaigns up and running quickly and efficiently.
            </p>
          </div>
        </div>
      </section>

      {/* Process Steps */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              PROCESS
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              For process working with customer at Golden AGENCY, follow four
              steps:
            </p>
          </div>

          {/* Step Navigation */}

          {/* Active Step Content */}

          {/* Process Timeline */}
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20 bg-gradient-to-r from-blue-50 to-purple-50">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              Why Choose Our Process?
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Our streamlined approach delivers results while minimizing risks
              and maximizing your success
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-3 gap-8">
            {benefits.map((benefit, index) => (
              <div
                key={index}
                className="card p-6 text-center group hover:shadow-2xl transition-all duration-300"
              >
                <div className="w-16 h-16 mx-auto mb-6 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center text-2xl transform group-hover:scale-110 transition-transform duration-300">
                  {benefit.icon}
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-4 group-hover:text-blue-600 transition-colors">
                  {benefit.title}
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  {benefit.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-blue-600 to-purple-600">
        <div className="container mx-auto px-6 text-center">
          <div className="max-w-3xl mx-auto">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Ready to Start Your Journey?
            </h2>
            <p className="text-xl text-blue-100 mb-8">
              Take the first step towards advertising success with our proven
              process and expert support.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-white text-blue-600 font-semibold px-8 py-4 rounded-full hover:bg-gray-100 transition-colors">
                Start Free Consultation
              </button>
              <button className="border-2 border-white text-white font-semibold px-8 py-4 rounded-full hover:bg-white hover:text-blue-600 transition-colors">
                Learn More
              </button>
            </div>
          </div>
        </div>
      </section>

      <Contact />
    </div>
  );
}
