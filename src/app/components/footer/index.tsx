import Image from "next/image";
import React from "react";
import Logo from "@/app/assets/logo-icon.png";
import Link from "next/link";
function Footer() {
  return (
    <div className=" container lg:px-0 mx-auto flex flex-wrap items-center gap-6 justify-between pt-[1.625rem] pb-[2.5rem] border-t border-gray-100 z-40">
      <div>
        <div className=" scale-[0.85] translate-x-[-0.65rem]">
          <Image alt="icon" width={97} src={Logo} />
        </div>
        <p className="text-gray-300 text-[0.875rem] leading-[1.375rem]">
          Copyright © 2025 Goden Agency
        </p>
      </div>
      <div className="flex gap-4 xs:gap-6 md:gap-10">
        <Link
          href="/"
          className="text-gray-300 hover:text-red-500 text-[0.75rem]  md:text-base leading-[1.25rem] md:leading-[1.5rem] font-normal"
        >
          Home
        </Link>
        <Link
          href="/about"
          className="text-gray-300 hover:text-red-500 text-[0.75rem]  md:text-base leading-[1.25rem] md:leading-[1.5rem] font-normal"
        >
          About
        </Link>
        <Link
          href="/process"
          className="text-gray-300 hover:text-red-500 text-[0.75rem]  md:text-base leading-[1.25rem] md:leading-[1.5rem] font-normal"
        >
          Process
        </Link>
        <Link
          href="/showCases"
          className="text-gray-300 hover:text-red-500 text-[0.75rem]  md:text-base leading-[1.25rem] md:leading-[1.5rem] font-normal"
        >
          ShowCases
        </Link>
      </div>
    </div>
  );
}

export default Footer;
