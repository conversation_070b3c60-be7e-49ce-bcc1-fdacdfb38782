"use client";
import React, { useState } from "react";
import FBQR from "@/app/assets/whatappsqr.png";
import adminQr from "@/app/assets/adminQR.png";
import jessicaQr from "@/app/assets/amydoqr.png";
import davidQr from "@/app/assets/channelqr.png";
import Link from "next/link";
import clsx from "clsx";
import Image from "next/image";
import emailjs from "@emailjs/browser";

function Page() {
  const [currentInfo, setCurrentInfo] = useState(1);
  const [currentImg, setCurrentImg] = useState(FBQR);
  const handleSubmit = () => {
    emailjs
      .send(
        "service_s6fa3r3",
        "template_b1avrzo",
        { message: "hello", name: "huy", sender: "hi" },
        {
          publicKey: "cv-gwQXOzaBI3HPyv",
        }
      )
      .then(
        (response) => {
          console.log("SUCCESS!", response.status, response.text);
        },
        (err) => {
          console.log("FAILED...", err);
        }
      );
  };
  const handleHoverInFoItem = (currentItem: number) => {
    setCurrentInfo(currentItem);
    if (currentItem == 1) {
      setCurrentImg(adminQr);
    }
    if (currentItem == 2) {
      setCurrentImg(jessicaQr);
    }
    if (currentItem == 3) {
      setCurrentImg(davidQr);
    }
    if (currentItem == 4) {
      setCurrentImg(FBQR);
    }
  };
  return (
    <div className="mt-5 lg:mt-3 relative" id="contact">
      <div className="container pt-1 lg:px-0 mx-auto relative">
        <div className="get-in-touch-wrapper">
          <div className="flex-1 z-30">
            <div className="get-in-touch_title-box">
              <h1 className="text-gray-800 text-3xl md:text-5xl lg:text-6xl font-bold leading-tight mb-6">
                <span className="relative inline-block">
                  <span className="text-cyan-600 drop-shadow-2xl text-glow shimmer">
                    Get In Touch
                  </span>
                  <div className="absolute inset-0 bg-gradient-to-r from-cyan-400/20 to-purple-400/20 blur-xl -z-10"></div>
                </span>
                <br />
                <span className="relative inline-block">
                  <span className="text-blue-700 drop-shadow-2xl font-extrabold">
                    With Our Team
                  </span>
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-400/30 to-pink-400/30 blur-2xl -z-10"></div>
                </span>
              </h1>
              <div>
                <p className="text-gray-700 text-lg md:text-xl font-medium leading-relaxed text-center mb-4">
                  <span className="inline-block">📞</span>
                  <span className="mx-2">
                    Ready to start your advertising journey?
                  </span>
                  <span className="text-blue-700 font-semibold">
                    Connect with our experts
                  </span>
                  <span className="mx-2">through multiple channels</span>
                  <span className="inline-block">🚀</span>
                </p>

                {/* Contact method badges */}
                <div className="flex flex-wrap justify-center gap-3">
                  <span className="px-4 py-2 bg-gradient-to-r from-orange-500/20 to-red-500/20 rounded-full text-orange-700 text-sm font-semibold border border-orange-400/30">
                    🌍 Vietnam Office
                  </span>

                  <span className="px-4 py-2 bg-gradient-to-r from-blue-500/20 to-cyan-500/20 rounded-full text-blue-700 text-sm font-semibold border border-blue-400/30">
                    💬 Telegram
                  </span>

                  <span className="px-4 py-2 bg-gradient-to-r from-green-500/20 to-emerald-500/20 rounded-full text-green-700 text-sm font-semibold border border-green-400/30">
                    📱 Phone Number
                  </span>
                </div>
              </div>
            </div>
            <div>
              <div className="mb-6">
                <div className="flex flex-col md:flex-row  justify-between md:gap-9 gap-1">
                  <p className="text-[rgb(70,70,70)] w-full max-w-[20.875rem] text-xs md:text-base leading-[1.8125rem]">
                    Xuan Thuy, Cau Giay, Ha Noi
                  </p>
                  <div className="md:text-right">
                    <p className="text-[rgb(70,70,70)] md:min-w-[9.125rem] font-bold text-lg md:text-lg leading-[1.5rem]">
                      +84 947376225
                    </p>
                    <p className="text-[rgb(70,70,70)] text-xs md:text-base leading-[2rem]"></p>
                  </div>
                </div>
              </div>

              <div className="flex flex-col justify-between gap-4">
                <div className="flex justify-between md:flex-row flex-col gap-4">
                  <div className="get-in-touch_contacts-box flex flex-col items-center">
                    <div
                      className={clsx(currentInfo == 1 && "info-item-active")}
                      onMouseEnter={() => handleHoverInFoItem(1)}
                      onClick={() => handleHoverInFoItem(1)}
                    >
                      <div className="get-in-touch_contacts-box__contact">
                        <span className="get-in-touch_nav__social-media-link">
                          <svg
                            viewBox="0 0 48 48"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <g id="SVGRepo_bgCarrier" strokeWidth="0"></g>
                            <g
                              id="SVGRepo_tracerCarrier"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            ></g>
                            <g id="SVGRepo_iconCarrier">
                              {" "}
                              <path
                                d="M41.4193 7.30899C41.4193 7.30899 45.3046 5.79399 44.9808 9.47328C44.8729 10.9883 43.9016 16.2908 43.1461 22.0262L40.5559 39.0159C40.5559 39.0159 40.3401 41.5048 38.3974 41.9377C36.4547 42.3705 33.5408 40.4227 33.0011 39.9898C32.5694 39.6652 24.9068 34.7955 22.2086 32.4148C21.4531 31.7655 20.5897 30.4669 22.3165 28.9519L33.6487 18.1305C34.9438 16.8319 36.2389 13.8019 30.8426 17.4812L15.7331 27.7616C15.7331 27.7616 14.0063 28.8437 10.7686 27.8698L3.75342 25.7055C3.75342 25.7055 1.16321 24.0823 5.58815 22.459C16.3807 17.3729 29.6555 12.1786 41.4193 7.30899Z"
                                fill=""
                                className="fill-item"
                              ></path>{" "}
                            </g>
                          </svg>
                        </span>
                        <span className="get-in-touch_contacts-box__contact-url">
                          <Link href={"https://t.me/ACLG28"} target="_blank">
                            Admin Telegram (https://t.me/ACLG28)
                          </Link>
                        </span>
                      </div>
                    </div>
                    <div className="imageContainer  relative w-[151px] h-[151px] rounded-lg mx-auto md:mx-[unset] get-in-touch_qr-box">
                      <Image src={currentImg} alt="img" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="flex-1 z-30 relative">
            <form className="flex flex-col gap-4 md:w-[33.75rem] input:md:h-14 relative gform">
              <input
                type="email"
                name="email"
                className="get-in-touch_form__input md:h-14 text-gray-100"
                placeholder="Your Email"
                required
              />
              <input
                type="text"
                name="subject"
                className="get-in-touch_form__input md:h-14"
                placeholder="Subject"
                required
              ></input>
              <textarea
                name="message"
                className="get-in-touch_form__input bg-transparent h-[16.5rem]"
                placeholder="Your Message"
                required
              ></textarea>
              <button
                className="button_btn-secondary-contained flex items-center justify-center duration-100 font-bold rounded px-1 py-1 w-[9.875rem] md:w-[11.625rem] md:h-[2.75rem] text-base mx-auto"
                onClick={handleSubmit}
              >
                <span>
                  <p className=" md:leading-[1.375rem] !text-white">
                    Send Message
                  </p>
                </span>
              </button>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Page;
