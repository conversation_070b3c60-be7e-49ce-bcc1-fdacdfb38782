"use client";
import Link from "next/link";
import React, { useEffect, useState } from "react";
import "./style.css";
import Logo from "@/app/assets/logo-icon.png";
import Image from "next/image";
import { usePathname } from "next/navigation";
import clsx from "clsx";
function Header() {
  const [isToggle, setIsToggle] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  const path = usePathname();

  const toggleButton = () => {
    const toggleElement = document.querySelector(".button-toggle");
    if (toggleElement?.classList) {
      toggleElement.classList.toggle("button-hidden");
    }
    if (toggleElement?.classList.contains("button-hidden")) {
      setIsToggle(true);
    } else {
      setIsToggle(false);
    }
  };
  const trackScrolling = () => {
    const wrappedElement = document.getElementById("header-item");
    if (wrappedElement) {
      const condition =
        wrappedElement.getBoundingClientRect().bottom <= window.scrollY;
      if (condition) {
        wrappedElement.classList.add("header-scroll");
      } else {
        wrappedElement.classList.remove("header-scroll");
      }
    }
  };

  const checkToggleButton = () => {
    const toggleElement = document.querySelector(".button-toggle");
    if (window.innerWidth >= 786) {
      toggleElement?.classList.remove("button-hidden");
      setIsToggle(false);
      setIsMobile(false);
    } else {
      setIsMobile(true);
    }
  };
  const clickHandleRouter = () => {
    const toggleElement = document.querySelector(".button-toggle");
    toggleElement?.classList.remove("button-hidden");
    setIsToggle(false);
  };
  useEffect(() => {
    checkToggleButton();
    window.addEventListener("resize", checkToggleButton);
    window.addEventListener("scroll", trackScrolling);
    return () => {
      window.removeEventListener("resize", checkToggleButton);
      window.removeEventListener("scroll", trackScrolling);
    };
  }, []);
  return (
    <header
      id="header-item"
      className={clsx(
        "fixed inset-x-0 top-0 z-50 transition-all duration-300",
        isToggle && isMobile && "bottom-0 bg-white/95 backdrop-blur-md"
      )}
    >
      <div className="glass-effect border-b border-white/20">
        <div className="container mx-auto px-6">
          <div
            className={clsx(
              "flex justify-between items-center py-4",
              isMobile && isToggle && "flex-col items-start"
            )}
          >
            {/* Logo */}
            <Link
              onClick={clickHandleRouter}
              className="flex items-center space-x-3 z-50"
              href="/"
            >
              <span className="text-xl font-bold bg-gradient-to-r from-red-500 to-orange-500 bg-clip-text text-transparent">
                Golden Agency
              </span>
            </Link>

            {/* Desktop Navigation */}
            <nav
              className={clsx(
                "hidden md:flex items-center space-x-8",
                isMobile && "hidden"
              )}
            >
              <Link href="/" onClick={clickHandleRouter}>
                <span
                  className={clsx(
                    "text-sm font-medium transition-colors duration-200 hover:text-blue-600",
                    path === "/" ? "text-blue-600" : "text-gray-700"
                  )}
                >
                  Home
                </span>
              </Link>
              <Link href="/about" onClick={clickHandleRouter}>
                <span
                  className={clsx(
                    "text-sm font-medium transition-colors duration-200 hover:text-blue-600",
                    path === "/about" ? "text-blue-600" : "text-gray-700"
                  )}
                >
                  About
                </span>
              </Link>
              <Link href="/process" onClick={clickHandleRouter}>
                <span
                  className={clsx(
                    "text-sm font-medium transition-colors duration-200 hover:text-blue-600",
                    path === "/process" ? "text-blue-600" : "text-gray-700"
                  )}
                >
                  Process
                </span>
              </Link>
              <Link href="/contact" onClick={clickHandleRouter}>
                <span
                  className={clsx(
                    "text-sm font-medium transition-colors duration-200 hover:text-blue-600",
                    path === "/contact" ? "text-blue-600" : "text-gray-700"
                  )}
                >
                  Contact Us
                </span>
              </Link>
              <Link href="/contact" onClick={clickHandleRouter}>
                <button className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-2 rounded-full text-sm font-semibold hover:shadow-lg transition-all duration-200 transform hover:scale-105">
                  Get Started
                </button>
              </Link>
            </nav>

            {/* Mobile Navigation Toggle */}
            <button
              onClick={toggleButton}
              className="md:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200"
              aria-label="Toggle navigation menu"
            >
              <div className="w-6 h-6 flex flex-col justify-center items-center">
                <span
                  className={clsx(
                    "block h-0.5 w-6 bg-gray-600 transition-all duration-200",
                    isToggle ? "rotate-45 translate-y-1" : "-translate-y-1"
                  )}
                ></span>
                <span
                  className={clsx(
                    "block h-0.5 w-6 bg-gray-600 transition-all duration-200",
                    isToggle ? "opacity-0" : "opacity-100"
                  )}
                ></span>
                <span
                  className={clsx(
                    "block h-0.5 w-6 bg-gray-600 transition-all duration-200",
                    isToggle ? "-rotate-45 -translate-y-1" : "translate-y-1"
                  )}
                ></span>
              </div>
            </button>

            {/* Mobile Navigation Menu */}
            {isToggle && isMobile && (
              <nav className="w-full mt-8 pb-8">
                <div className="flex flex-col space-y-6">
                  <Link href="/" onClick={clickHandleRouter}>
                    <span
                      className={clsx(
                        "block text-lg font-medium transition-colors duration-200 hover:text-blue-600",
                        path === "/" ? "text-blue-600" : "text-gray-700"
                      )}
                    >
                      Home
                    </span>
                  </Link>
                  <Link href="/about" onClick={clickHandleRouter}>
                    <span
                      className={clsx(
                        "block text-lg font-medium transition-colors duration-200 hover:text-blue-600",
                        path === "/about" ? "text-blue-600" : "text-gray-700"
                      )}
                    >
                      About
                    </span>
                  </Link>
                  <Link href="/process" onClick={clickHandleRouter}>
                    <span
                      className={clsx(
                        "block text-lg font-medium transition-colors duration-200 hover:text-blue-600",
                        path === "/process" ? "text-blue-600" : "text-gray-700"
                      )}
                    >
                      Process
                    </span>
                  </Link>
                  <Link href="/contact" onClick={clickHandleRouter}>
                    <span
                      className={clsx(
                        "block text-lg font-medium transition-colors duration-200 hover:text-blue-600",
                        path === "/contact" ? "text-blue-600" : "text-gray-700"
                      )}
                    >
                      Contact
                    </span>
                  </Link>
                  <Link href="/contact" onClick={clickHandleRouter}>
                    <button className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-full font-semibold hover:shadow-lg transition-all duration-200 w-full">
                      Get Started
                    </button>
                  </Link>
                </div>
              </nav>
            )}
          </div>
        </div>
      </div>
    </header>
  );
}

export default Header;
