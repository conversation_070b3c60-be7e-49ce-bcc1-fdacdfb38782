.button-toggle {
  margin-top: 30px;
  padding: 5px;
}

.isBlock {
  display: block !important;
}
.button-toggle div {
  height: 3px;
  width: 40px;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 0.5s;
}
.button-hidden:hover div {
  background-color: rgb(239 68 68) !important;
}
.button-hidden div:first-child {
  transform: rotate(45deg) translateY(0.28rem);
}
.button-hidden div:nth-of-type(2) {
  display: none;
}

.button-hidden div:last-child {
  --tw-translate-y: -0.3rem;
  --tw-rotate: -45deg;
  transform: translateY(-0.4rem) rotate(-45deg);
}
#header-item {
  color: white;
}
#header-item.header-scroll {
  color: rgb(155, 155, 155);
  background-color: white;
  box-shadow: 1px 1px 22px rgba(0, 0, 0, 0.1);
}
