"use client";
import { useState, useEffect, useRef } from "react";
import Image from "next/image";
import { Swiper, SwiperSlide } from "swiper/react";
import {
  Navigation,
  Pagination,
  Autoplay,
  EffectCoverflow,
} from "swiper/modules";

// Import Swiper styles
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import "swiper/css/effect-coverflow";
import Bg from "@/app/assets/bgHomePage.pdf.png";
import MetaIcons from "@/app/assets/metaIcon.webp";
import AdspowerIcon from "@/app/assets/2-ADSPOWER..png";
import PayoneerIcon from "@/app/assets/3-PAYONEER.png";
import BinanceIcon from "@/app/assets/binanceIcon.webp";
import AcbIcon from "@/app/assets/acbIcon.avif";
import GoLoginIcon from "@/app/assets/6-gologin.webp";
import MusicImage from "@/app/assets/music.png";

import Contact from "@/app/components/contact";

// Counter Component
function Counter({
  end,
  duration = 2000,
  suffix = "",
  prefix = "",
}: {
  end: number;
  duration?: number;
  suffix?: string;
  prefix?: string;
}) {
  const [count, setCount] = useState(0);
  const [isVisible, setIsVisible] = useState(false);
  const counterRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !isVisible) {
          setIsVisible(true);
        }
      },
      { threshold: 0.1 }
    );

    if (counterRef.current) {
      observer.observe(counterRef.current);
    }

    return () => observer.disconnect();
  }, [isVisible]);

  useEffect(() => {
    if (!isVisible) return;

    let startTime: number;
    let animationFrame: number;

    const animate = (timestamp: number) => {
      if (!startTime) startTime = timestamp;
      const progress = Math.min((timestamp - startTime) / duration, 1);

      // Easing function for smooth animation
      const easeOutQuart = 1 - Math.pow(1 - progress, 4);
      const currentCount = Math.floor(easeOutQuart * end);

      setCount(currentCount);

      if (progress < 1) {
        animationFrame = requestAnimationFrame(animate);
      }
    };

    animationFrame = requestAnimationFrame(animate);

    return () => {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame);
      }
    };
  }, [isVisible, end, duration]);

  return (
    <div ref={counterRef} className="text-3xl font-bold">
      {prefix}
      {count.toLocaleString()}
      {suffix}
    </div>
  );
}

export default function Home() {
  const partnerLogos = [
    MetaIcons,
    AdspowerIcon,
    PayoneerIcon,
    BinanceIcon,
    AcbIcon,
    GoLoginIcon,
  ];

  const testimonials = [
    {
      id: 1,
      name: "Mr C - Account executive ",
      content:
        "The account quality here is really good, the account rarely has problems. If there is any problem, the staff immediately solves it for me, I am very satisfied.",
      rating: 5,
    },
    {
      id: 2,
      name: "Mike- Producer",
      content:
        "When I worked with another agency in the past, I ran into a lot of issues, like slow speed, lengthy response times, and frequently interrupted deposit systems, which had an impact on the advertising campaign. But everything changed drastically after relocating to IRON MARKETING AGENCY. Quick service, dependable account, committed assistance, and increased security when running advertisements.",
      rating: 5,
    },
    {
      id: 3,
      name: "Matthew- Designer",
      content:
        "I had to look for a source of advertising account leasing services for a very long time before I could use IRON MARKETING AGENCY's services. It really surprised me how good these advertising accounts were. For my business, the unlimited budget is effective. The account almost never expires, no matter how many campaigns I run.",
      rating: 5,
    },
    {
      id: 4,
      name: "Jane- Project manager ",
      content:
        "Because the ads weren't accepted, I had a lot of problems and sometimes went crazy. One day, after receiving IRON MARKETING AGENCY's message, I contacted the administrator. Luckily, when I used your account, my advertising worked perfectly. Sometimes there were problems, but they were all quickly resolved. The fact that my company's profits increased that month as a result is something I consider a miracle.",
      rating: 5,
    },
  ];

  const gotoBottom = () => {
    const element = document.getElementById("contact");
    if (element) {
      element.scrollIntoView({ behavior: "smooth" });
    }
  };

  return (
    <div className="overflow-x-hidden">
      {/* Modern Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
        <div className="absolute inset-0 bg-black/20"></div>
        <Image
          src={Bg}
          alt="Hero Background"
          fill
          className="object-cover opacity-30"
          priority
        />

        <div className="relative z-10 container mx-auto px-6 text-center">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-4xl md:text-6xl lg:text-6xl font-bold text-white mb-6 leading-tight">
              <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                FACEBOOK ADS ACCOUNT RENTAL SERVICE PROVIDER
              </span>{" "}
            </h1>
            <p className="text-xl md:text-2xl text-gray-200 mb-8 max-w-2xl mx-auto">
              Quality is always the first choice
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <button
                onClick={gotoBottom}
                className="group bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold px-8 py-4 rounded-full transition-all duration-300 transform hover:scale-105 hover:shadow-xl flex items-center gap-3"
              >
                Get In Touch
                <svg
                  className="w-5 h-5 group-hover:translate-x-1 transition-transform"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M13 7l5 5m0 0l-5 5m5-5H6"
                  />
                </svg>
              </button>
            </div>
          </div>
        </div>

        {/* Floating Stats */}
        <div className="absolute bottom-20 left-1/2 transform -translate-x-1/2 w-full max-w-4xl px-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="glass-effect rounded-2xl p-6 text-center text-white">
              <div className="text-blue-400">
                <Counter end={600} suffix="+" duration={2500} />
              </div>
              <div className="text-sm text-gray-300">Happy Customers</div>
            </div>
            <div className="glass-effect rounded-2xl p-6 text-center text-white">
              <div className="text-purple-400">
                <Counter end={4000} suffix="+" duration={2500} />
              </div>
              <div className="text-sm text-gray-300">Active Accounts</div>
            </div>
            <div className="glass-effect rounded-2xl p-6 text-center text-white">
              <div className="text-pink-400">
                <Counter end={3} suffix="M+" prefix="$" duration={2500} />
              </div>
              <div className="text-sm text-gray-300">Ad Spend Managed</div>
            </div>
          </div>
        </div>
      </section>

      {/* Music Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-6">
          <div className="flex justify-center">
            <Image
              src={MusicImage}
              alt="Music"
              width={800}
              height={600}
              className="object-contain"
              priority
            />
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-20 bg-gradient-to-r from-blue-50 to-purple-50">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              WALL OF ACHIEVEMENTS
            </h2>
            <p className="text-xl text-gray-600">
              What we've achieved together with our clients
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="card p-8 flex items-center space-x-6">
              <div className="w-16 h-16 rounded-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center text-2xl flex-shrink-0">
                👥
              </div>
              <div className="flex-grow">
                <div className="text-4xl font-bold text-blue-600 mb-2">
                  <Counter end={50} suffix="+" duration={3000} />
                </div>
                <p className="text-gray-600 text-base">experienced staffs</p>
              </div>
            </div>
            <div className="card p-8 flex items-center space-x-6">
              <div className="w-16 h-16 rounded-full bg-gradient-to-r from-purple-500 to-purple-600 flex items-center justify-center text-2xl flex-shrink-0">
                📊
              </div>
              <div className="flex-grow">
                <div className="text-4xl font-bold text-purple-600 mb-2">
                  <Counter end={600} suffix="+" duration={3000} />
                </div>
                <p className="text-gray-600 text-base">global customers</p>
              </div>
            </div>
            <div className="card p-8 flex items-center space-x-6">
              <div className="w-16 h-16 rounded-full bg-gradient-to-r from-pink-500 to-pink-600 flex items-center justify-center text-2xl flex-shrink-0">
                💰
              </div>
              <div className="flex-grow">
                <div className="text-4xl font-bold text-pink-600 mb-2">
                  <Counter end={4000} suffix="+" duration={2000} />
                </div>
                <p className="text-gray-600 text-base">
                  Active accounts every month
                </p>
              </div>
            </div>
            <div className="card p-8 flex items-center space-x-6">
              <div className="w-16 h-16 rounded-full bg-gradient-to-r from-pink-500 to-pink-600 flex items-center justify-center text-2xl flex-shrink-0">
                💰
              </div>
              <div className="flex-grow">
                <div className="text-4xl font-bold text-pink-600 mb-2">
                  <Counter end={3} suffix="M+" prefix="$" duration={2000} />
                </div>
                <p className="text-gray-600 text-base">
                  Spending of one of the big customers reached 3 million
                  USD/month
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Partners Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-6">
          <div className="text-center mb-12">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Trusted Partners
            </h3>
            <p className="text-gray-600">
              We work with industry-leading platforms and tools
            </p>
          </div>

          <div className="flex flex-wrap justify-center items-center gap-8 opacity-60 hover:opacity-100 transition-opacity duration-300">
            {partnerLogos.map((logo, index) => (
              <div
                key={index}
                className="grayscale hover:grayscale-0 transition-all duration-300"
              >
                <Image
                  src={logo}
                  alt={`Partner ${index + 1}`}
                  width={120}
                  height={60}
                  className="object-contain"
                />
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 bg-gradient-to-r from-gray-50 to-blue-50">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              And What We Made...
            </h2>
            <p className="text-xl text-gray-600">
              Let's see what customers say about us
            </p>
          </div>

          <div className="max-w-6xl mx-auto">
            <Swiper
              modules={[Navigation, Pagination, Autoplay, EffectCoverflow]}
              effect="coverflow"
              grabCursor={true}
              centeredSlides={true}
              slidesPerView={3}
              spaceBetween={-50}
              speed={800}
              navigation={true}
              pagination={{
                clickable: true,
                dynamicBullets: true,
              }}
              autoplay={{
                delay: 5000,
                disableOnInteraction: false,
                pauseOnMouseEnter: true,
              }}
              loop={true}
              coverflowEffect={{
                rotate: 50,
                stretch: -60,
                depth: 300,
                modifier: 1,
                slideShadows: true,
              }}
              breakpoints={{
                320: {
                  slidesPerView: 1,
                  spaceBetween: 0,
                  coverflowEffect: {
                    rotate: 45,
                    stretch: 0,
                    depth: 200,
                    modifier: 1,
                    slideShadows: true,
                  },
                },
                768: {
                  slidesPerView: 2,
                  spaceBetween: -30,
                  coverflowEffect: {
                    rotate: 45,
                    stretch: -40,
                    depth: 250,
                    modifier: 1,
                    slideShadows: true,
                  },
                },
                1024: {
                  slidesPerView: 3,
                  spaceBetween: -50,
                  coverflowEffect: {
                    rotate: 50,
                    stretch: -60,
                    depth: 300,
                    modifier: 1,
                    slideShadows: true,
                  },
                },
              }}
              className="testimonials-swiper"
            >
              {testimonials.map((testimonial, index) => (
                <SwiperSlide key={testimonial.id}>
                  <div className="testimonial-card relative p-8 flex flex-col aspect-square h-96 w-96 mx-auto bg-white rounded-2xl shadow-xl border border-gray-200 hover:shadow-2xl transition-all duration-300 group">
                    {/* Stars Rating */}
                    <div className="flex mb-6 justify-center">
                      {[...Array(testimonial.rating)].map((_, i) => (
                        <svg
                          key={i}
                          className="w-5 h-5 text-yellow-400 fill-current mx-1"
                          viewBox="0 0 20 20"
                        >
                          <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z" />
                        </svg>
                      ))}
                    </div>

                    {/* Testimonial Content */}
                    <div className="flex-grow flex flex-col justify-center">
                      <p className="text-gray-700 italic text-sm leading-relaxed text-center mb-6">
                        "{testimonial.content}"
                      </p>
                    </div>

                    {/* Customer Name */}
                    <div className="text-center">
                      <div className="font-semibold text-gray-900 text-sm bg-gray-50 rounded-lg py-3 px-4 border border-gray-100">
                        {testimonial.name}
                      </div>
                    </div>

                    {/* Card Number Badge */}
                    <div className="absolute -top-2 -right-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white text-xs font-bold w-8 h-8 rounded-full flex items-center justify-center shadow-lg">
                      {index + 1}
                    </div>
                  </div>
                </SwiperSlide>
              ))}
            </Swiper>
          </div>
        </div>
      </section>

      <Contact />
    </div>
  );
}
