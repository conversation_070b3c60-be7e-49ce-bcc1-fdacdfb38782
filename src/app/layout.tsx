import type { Metadata } from "next";
import { Inter, Poppins } from "next/font/google";
import "./globals.css";
import Header from "./components/header";
import Footer from "./components/footer";
import Logo from "./assets/logo-icon.png";
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import "swiper/css/effect-coverflow";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
  display: "swap",
});

const poppins = Poppins({
  variable: "--font-poppins",
  subsets: ["latin"],
  weight: ["300", "400", "500", "600", "700", "800"],
  display: "swap",
});

export const metadata: Metadata = {
  title: "Golden Agency - Premium Facebook Ads Solutions",
  description:
    "Professional Facebook Ads account rental service with 600+ satisfied customers worldwide. Quality guaranteed, 24/7 support, and unlimited budget solutions.",
  openGraph: {
    title: "Golden Agency - Premium Facebook Ads Solutions",
    description:
      "Professional Facebook Ads account rental service with 600+ satisfied customers worldwide.",
    images: "./assets/logo-icon.png",
    type: "website",
  },
  icons: {
    icon: "./assets/logo-icon.png",
  },
  keywords: [
    "Facebook Ads",
    "Account Rental",
    "Digital Marketing",
    "Advertising Solutions",
  ],
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={`${inter.variable} ${poppins.variable}`}>
      <body className="antialiased min-h-full bg-gradient-to-br from-slate-50 via-white to-blue-50">
        <Header />
        <main className="relative">{children}</main>
        <Footer />
      </body>
    </html>
  );
}
