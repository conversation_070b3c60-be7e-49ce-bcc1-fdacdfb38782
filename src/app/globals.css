@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #ffffff;
  --foreground: #1e293b;
  --primary: #3b82f6;
  --primary-dark: #1d4ed8;
  --secondary: #f59e0b;
  --accent: #8b5cf6;
  --success: #10b981;
  --danger: #ef4444;
  --warning: #f59e0b;
  --info: #06b6d4;
  --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --gradient-accent: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --shadow-soft: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-medium: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-large: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --border-radius: 12px;
  --border-radius-lg: 16px;
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

* {
  scroll-behavior: smooth;
}

body {
  color: var(--foreground);
  font-family: var(--font-inter), system-ui, -apple-system, sans-serif;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: var(--font-poppins), system-ui, -apple-system, sans-serif;
  font-weight: 600;
  line-height: 1.2;
}

.glass-effect {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
}

.gradient-text {
  background: linear-gradient(135deg, #ef4444 0%, #f97316 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.golden-gradient {
  background: linear-gradient(135deg, #ef4444 0%, #f97316 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.btn-primary {
  background: var(--gradient-primary);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  padding: 12px 24px;
  font-weight: 600;
  transition: var(--transition);
  box-shadow: var(--shadow-soft);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

.card {
  /* background: white;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-soft);
  transition: var(--transition);
  border: 1px solid rgba(226, 232, 240, 0.5); */
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-large);
}

/* home page */

.banner {
  -webkit-clip-path: ellipse(80% 93% at 50% 7%);
  clip-path: ellipse(80% 93% at 50% 7%);
  position: relative;
  display: flex;
  height: 18rem;
  width: 100%;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.banner__span {
  box-sizing: border-box;
  display: block;
  overflow: hidden;
  width: initial;
  height: initial;
  background: none;
  opacity: 1;
  border: 0px;
  margin: 0px;
  padding: 0px;
  position: absolute;
  inset: 0px;
}

.banner__img {
  position: absolute;
  inset: 0px;
  box-sizing: border-box;
  padding: 0px;
  border: none;
  margin: auto;
  display: block;
  width: 0px;
  height: 0px;
  min-width: 100%;
  max-width: 100%;
  min-height: 100%;
  max-height: 100%;
  filter: brightness(50%);
  background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5));
}
.banner__img::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgb(0, 0, 0);
}

.banner__item p {
  color: rgb(136, 136, 136);
}
.banner__item:hover p {
  color: white;
}

.what-are-we-talking-about_title {
  position: relative;
  height: 100%;
  max-width: 21.215rem;
  font-size: 2.5rem;
  font-weight: 700;
  text-transform: capitalize;
  font-style: normal;
  line-height: 3rem;
  -webkit-text-fill-color: transparent;
  -webkit-text-stroke-width: 1px;
  -webkit-text-stroke-color: #673ab7;
}
.what-are-we-talking-about_title:after {
  position: absolute;
  left: -0.8%;
  top: -1%;
  height: 100%;
  width: 100%;
  color: transparent;
  --tw-content: attr(data-value);
  content: var(--tw-content);
  -webkit-text-fill-color: transparent;
  -webkit-text-stroke-width: 1px;
  -webkit-text-stroke-color: #8c0019;
}

.what-are-we-talking:hover {
  background: -webkit-linear-gradient(bottom, #321c93, #673ab7);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.get-in-touch-wrapper {
  margin-top: 2rem;
  margin-bottom: 3.5rem;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 2rem;
}
.get-in-touch_title-box {
  margin-bottom: 2rem;
  display: flex;
  width: 100%;
  flex-direction: column;
  gap: 0.375rem;
  --tw-border-opacity: 1;
  border-bottom: 1px;
  border-color: rgb(224 224 224 / var(--tw-border-opacity));
  border-style: solid;
  padding-bottom: 1rem;
  line-height: 3.0625rem;
}
.get-in-touch_title-box__title {
  position: relative;
  min-width: 15rem;
  font-size: 2.5rem;
  font-weight: 700;
  -webkit-text-fill-color: transparent;
  -webkit-text-stroke-width: 1px;
  -webkit-text-stroke-color: #ef6c3f;
}
.get-in-touch_title-box__title::after {
  position: absolute;
  left: -0.8%;
  top: -1%;
  color: transparent;
  --tw-content: attr(data-title);
  content: var(--tw-content);
  -webkit-text-fill-color: transparent;
  -webkit-text-stroke-width: 1px;
  -webkit-text-stroke-color: #673ab7;
}

.get-in-touch_form__input {
  resize: none;
  width: 100%;
  border-radius: 0.25rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(170 170 170 / var(--tw-border-opacity));
  background-color: transparent;
  padding: 1.125rem 1rem;
  font-size: 1.125rem;
  --tw-text-opacity: 1;
  color: rgb(85 85 85);
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.get-in-touch_form__input:focus {
  --tw-border-opacity: 1;
  border-color: #8c0019;
  --tw-text-opacity: 1;
  color: #8c0019;
}
.button_btn-secondary-contained {
  border: 1px solid transparent;
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
  --tw-gradient-from: black;
  --tw-gradient-stops: var(--tw-gradient-from),
    var(--tw-gradient-to, rgb(103 58 183 / 0));
  --tw-gradient-to: #004d8c;
  --tw-text-opacity: 1;
  color: rgb(255 255 255);
}

.CategoryBox_content__detail:hover .CategoryBox_logo__icon--fill {
  fill: #8c0019;
}

.CategoryBox_content__detail:hover .CategoryBox_logo__icon--stroke {
  stroke: #8c0019;
}
.CategoryBox_content__detail:hover .CategoryBox_detail__content__title {
  color: #8c0019;
}
.CategoryBox_content__detail:hover .CategoryBox_detail__logo {
  box-shadow: 0 4px 32px rgba(0, 0, 0, 0.1);
  border-style: none;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255);
}
.get-in-touch_contacts-box {
  flex-grow: 1;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  gap: 1.5rem;
}
.get-in-touch_contacts-box__contact {
  display: flex;
  cursor: default;
  align-items: center;
  gap: 0.75rem;
  transition-property: color, background-color, border-color, fill, stroke,
    opacity, box-shadow, transform, filter, -webkit-text-decoration-color,
    -webkit-backdrop-filter;
  transition-property: color, background-color, border-color,
    text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter,
    backdrop-filter;
  transition-property: color, background-color, border-color,
    text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter,
    backdrop-filter, -webkit-text-decoration-color, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 0.15s;
}

.get-in-touch_nav__social-media-link {
  padding: 10px;
  display: flex;
  height: 2.5rem;
  width: 2.5rem;
  cursor: pointer;
  align-items: center;
  justify-content: center;
  border-radius: 9999px;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255);
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 0.5s;
  box-shadow: inset 0 -1px 4px rgba(0, 0, 0, 0.1);
}
.fill-item {
  fill: black;
  stroke: black;
}
.info-item-active .fill-item {
  fill: #8c0019;
  stroke: #8c0019;
}
.info-item-active .get-in-touch_nav__social-media-link {
  box-shadow: 0 4px 4px rgba(55, 42, 42, 0.1);
}
.info-item-active .get-in-touch_contacts-box__contact-url {
  --tw-text-opacity: 1;
  color: #8c0019;
  transition-property: color, background-color, border-color, fill, stroke,
    opacity, box-shadow, transform, filter, -webkit-text-decoration-color,
    -webkit-backdrop-filter;
  transition-property: color, background-color, border-color,
    text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter,
    backdrop-filter;
  transition-property: color, background-color, border-color,
    text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter,
    backdrop-filter, -webkit-text-decoration-color, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 0.15s;
}

.get-in-touch_contacts-box__contact-url {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 21rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: none;
  width: 100%;
  cursor: pointer;
  font-size: 1.125rem;
  line-height: 1.625;
  --tw-text-opacity: 1;
  color: rgb(85 85 85);
}

@media (min-width: 976px) {
  .banner {
    height: 50rem;
  }
  .what-are-we-talking-about_title {
    max-width: 15.93rem;
  }
  .get-in-touch-wrapper {
    margin-top: 5rem;
    justify-content: space-between;
  }
  .get-in-touch_title-box__title {
    min-width: 24rem;
    font-size: 4rem;
  }
}

@media (min-width: 768px) {
  .what-are-we-talking-about_title {
    padding-right: 1.5rem;
  }
  .what-are-we-talking-about_title:after {
    padding-right: 1.5rem;
  }
  .get-in-touch-wrapper {
    margin-bottom: 4.625rem;
    gap: 5.9375rem;
  }
  .get-in-touch_title-box__title {
    width: 24.5rem;
    font-size: 3.5rem;
    line-height: 4.875rem;
  }
  .get-in-touch_contacts-box {
    flex-direction: column;
    justify-content: center;
    gap: 0.75rem;
  }
  .get-in-touch_contacts-box__contact-url {
    display: inline;
  }
  .get-in-touch_qr-box {
    display: block;
  }
}
@media screen and (min-width: 688px) and (max-width: 767px) {
  .banner_wrapper {
    width: 70% !important;
  }
}
@media (min-width: 480px) {
  .banner_wrapper {
    width: 78%;
  }
}
@media (min-width: 1110px) {
  .get-in-touch-wrapper {
    flex-wrap: nowrap;
  }
}

/* Enhanced 3D Coverflow with Overlapping Effect */
.testimonials-swiper {
  padding-bottom: 120px;
  padding-top: 60px;
  perspective: 2000px;
  overflow: visible;
}

.testimonials-swiper .swiper-wrapper {
  perspective: 2000px;
  transform-style: preserve-3d;
}

.testimonials-swiper .swiper-slide {
  transform-style: preserve-3d;
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  will-change: transform;
}

.testimonials-swiper .swiper-slide-active {
  z-index: 20;
  transform: scale(1.15) translateZ(100px);
}

.testimonials-swiper .swiper-slide-prev,
.testimonials-swiper .swiper-slide-next {
  z-index: 15;
  opacity: 0.85;
  transform: scale(1.05) translateZ(50px);
}

.testimonials-swiper
  .swiper-slide:not(.swiper-slide-active):not(.swiper-slide-prev):not(
    .swiper-slide-next
  ) {
  opacity: 0.7;
  transform: scale(0.95) translateZ(0px);
}

/* Enhanced slide shadows for better depth */
.testimonials-swiper .swiper-slide-shadow-left,
.testimonials-swiper .swiper-slide-shadow-right {
  background: linear-gradient(to right, rgba(0, 0, 0, 0.4), transparent);
  border-radius: 16px;
}

/* Overlapping effect enhancement */
.testimonials-swiper .swiper-slide {
  margin-left: -20px;
  margin-right: -20px;
}

.testimonials-swiper .swiper-pagination {
  bottom: 20px;
}

.testimonials-swiper .swiper-pagination-bullet {
  background: #cbd5e1;
  opacity: 1;
  width: 12px;
  height: 12px;
  transition: all 0.3s ease;
}

.testimonials-swiper .swiper-pagination-bullet-active {
  background: #ef4444;
  transform: scale(1.3);
  box-shadow: 0 0 10px rgba(239, 68, 68, 0.5);
}

/* Enhanced Testimonial Card Styles for Coverflow */
.testimonials-swiper .testimonial-card {
  transform-style: preserve-3d;
  transition: all 0.5s ease;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
  border: 2px solid rgba(255, 255, 255, 0.8);
}

.testimonials-swiper .testimonial-card:hover {
  transform: translateY(-8px) rotateY(5deg);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
}

.testimonials-swiper .swiper-slide-active .testimonial-card {
  box-shadow: 0 30px 60px rgba(0, 0, 0, 0.25);
  border: 2px solid rgba(59, 130, 246, 0.3);
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.95),
    rgba(249, 250, 251, 0.95)
  );
}

.testimonials-swiper .swiper-slide-prev .testimonial-card,
.testimonials-swiper .swiper-slide-next .testimonial-card {
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.18);
}

/* Add depth to non-active cards */
.testimonials-swiper .swiper-slide:not(.swiper-slide-active) .testimonial-card {
  filter: brightness(0.95);
}

/* 3D Hover Animations */
@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
}

.testimonials-swiper .group:hover .floating-star {
  animation: float 2s ease-in-out infinite;
}

/* Enhanced 3D Card Effects */
.testimonials-swiper .group {
  transform-style: preserve-3d;
}

.testimonials-swiper .group:hover {
  transform: scale(1.05) rotateX(5deg) rotateY(-5deg);
}

/* Glowing Border Effect */
.testimonials-swiper .group::before {
  content: "";
  position: absolute;
  inset: -2px;
  background: linear-gradient(45deg, #3b82f6, #8b5cf6, #ec4899, #3b82f6);
  border-radius: 1rem;
  opacity: 0;
  transition: opacity 0.5s ease;
  z-index: -1;
  animation: rotate-border 3s linear infinite;
}

.testimonials-swiper .group:hover::before {
  opacity: 0.7;
}

@keyframes rotate-border {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.testimonials-swiper .swiper-button-next,
.testimonials-swiper .swiper-button-prev {
  color: #ef4444;
  width: 50px;
  height: 50px;
  margin-top: -25px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.testimonials-swiper .swiper-button-next:after,
.testimonials-swiper .swiper-button-prev:after {
  font-size: 18px;
  font-weight: bold;
}

.testimonials-swiper .swiper-button-next:hover,
.testimonials-swiper .swiper-button-prev:hover {
  color: #dc2626;
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.testimonials-swiper .swiper-button-prev {
  left: -60px;
}

.testimonials-swiper .swiper-button-next {
  right: -60px;
}

/* Enhanced 3D Card Effects */
.testimonials-swiper .swiper-slide {
  height: auto;
  display: flex;
  transition: all 0.4s ease;
}

/* Removed conflicting card styles that were covering content */

/* Mobile optimizations */
@media (max-width: 1023px) {
  .testimonials-swiper .swiper-button-next,
  .testimonials-swiper .swiper-button-prev {
    display: none;
  }
}

@media (max-width: 768px) {
  .testimonials-swiper {
    padding-bottom: 60px;
  }
}

/* Coverflow specific enhancements */
@media (min-width: 1024px) {
  .testimonials-swiper .swiper-slide {
    transition: all 0.5s ease;
  }

  .testimonials-swiper .swiper-slide-shadow-left,
  .testimonials-swiper .swiper-slide-shadow-right {
    background-image: linear-gradient(
      to right,
      rgba(0, 0, 0, 0.3),
      rgba(0, 0, 0, 0)
    );
    border-radius: 16px;
  }
}
