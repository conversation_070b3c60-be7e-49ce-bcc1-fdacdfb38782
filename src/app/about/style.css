.colleagues_title {
  position: relative;
  max-width: 18rem;
  font-size: 2.5rem;
  font-weight: 700;
  text-transform: capitalize;
  font-style: normal;
  line-height: 3.063rem;
  -webkit-text-fill-color: transparent;
  -webkit-text-stroke-width: 1px;
  -webkit-text-stroke-color: rgb(239, 68, 68);
}

.colleagues_title:after {
  position: absolute;
  left: -0.5%;
  top: -1.5%;
  height: 100%;
  width: 100%;
  color: transparent;
  --tw-content: attr(data-value);
  content: var(--tw-content);
  -webkit-text-fill-color: transparent;
  -webkit-text-stroke-width: 1px;
  -webkit-text-stroke-color: #ef6c3f;
}
@media (min-width: 480px) {
  .colleagues_title {
    max-width: 21.215rem;
    font-size: 3rem;
    line-height: 3.675rem;
  }
}
@media (min-width: 768px) {
  .colleagues_title {
    min-width: 30.5rem;
  }
}

@media (min-width: 976px) {
  .colleagues_title {
    max-width: 15.9375rem;
  }
}
