"use client";
import Image from "next/image";
import Bg from "@/app/assets/bgHomePage.pdf.png";

import Team1 from "@/app/assets/t1.png";
import Team2 from "@/app/assets/t2.png";
import Team3 from "@/app/assets/t3.png";
import Team4 from "@/app/assets/t4.png";
import Team5 from "@/app/assets/bp.jpg";
// Using placeholder for the 6th team member
import Contact from "@/app/components/contact";

export default function About() {
  const teamMembers = [
    {
      id: 1,
      image: Team1,
      name: "<PERSON>",
      role: "CEO & Founder",
      description:
        "Staff with more than 6 years of experience in the advertising industry",
      linkedin: "#",
    },
    {
      id: 2,
      image: Team2,
      name: "<PERSON>",
      role: "Marketing Director",
      description: "Enthusiastic and attentive when customers need",
      linkedin: "#",
    },
    {
      id: 3,
      image: Team3,
      name: "<PERSON>",
      role: "Technical Lead",
      description: "Timely support 24/7 regardless of holidays ",
      linkedin: "#",
    },
    {
      id: 4,
      image: Team4,
      name: "<PERSON>",
      role: "Account Manager",
      description:
        "Thoroughly handle problems for customers, always put customers at the center",
      linkedin: "#",
    },
  ];

  const values = [
    {
      icon: "🎯",
      title: "Mission-Driven",
      description:
        "Empowering businesses to achieve their advertising goals through premium Facebook account solutions and expert guidance.",
    },
    {
      icon: "👥",
      title: "Customer-Centric",
      description:
        "Every decision we make is focused on delivering exceptional value, support, and results for our clients worldwide.",
    },
    {
      icon: "🚀",
      title: "Innovation",
      description:
        "Continuously improving our services and staying ahead of industry trends, platform changes, and emerging technologies.",
    },
    {
      icon: "🤝",
      title: "Trust & Reliability",
      description:
        "Building long-term partnerships through consistent quality, transparent communication, and guaranteed results.",
    },
  ];

  const stats = [
    { number: "600+", label: "Happy Clients" },
    { number: "4000+", label: "Active Accounts" },
    { number: "$3M+", label: "Ad Spend Managed" },
    { number: "99.9%", label: "Uptime Guarantee" },
  ];

  return (
    <div className="overflow-x-hidden">
      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
        <div className="absolute inset-0 bg-black/40"></div>

        {/* Fallback Image */}

        {/* Video Background */}
        <video
          className="absolute inset-0 w-full h-full object-cover opacity-30 z-10"
          autoPlay
          muted
          loop
          playsInline
          preload="auto"
          onError={(e) => {
            e.currentTarget.style.display = "none";
          }}
        >
          <source src="/video.mp4" type="video/mp4" />
          <source src="/assets/video.mp4" type="video/mp4" />
        </video>

        <div className="relative z-10 container mx-auto px-6 text-center">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold text-white mb-6 leading-tight">
              About{" "}
              <span className="bg-gradient-to-r from-red-400 to-orange-400 bg-clip-text text-transparent">
                Golden Agency
              </span>
            </h1>
            <p className="text-xl md:text-2xl text-gray-200 mb-8 max-w-3xl mx-auto">
              We are a dedicated team of Facebook advertising experts committed
              to providing premium account solutions and exceptional service to
              businesses worldwide.
            </p>
          </div>
        </div>
      </section>

      {/* Mission Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-6">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-8">
              BRAND 'S{" "}
              <span className="bg-gradient-to-r from-red-500 to-orange-500 bg-clip-text text-transparent font-bold">
                MISSION
              </span>
            </h2>
            <p className="text-xl text-gray-600 leading-relaxed mb-12">
              GOLDEN AGENCY was born in the context of the advertising industry
              growing strongly with thousands of competitions, with the desire
              to provide customers with an optimal solution for Facebook ads
              accounts at a reasonable price.
            </p>

            {/* <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
                {stats.map((stat, index) => (
                  <div key={index} className="text-center">
                    <div className="text-3xl md:text-4xl font-bold text-blue-600 mb-2">
                      {stat.number}
                    </div>
                    <div className="text-gray-600">{stat.label}</div>
                  </div>
                ))}
              </div> */}
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-20 bg-gradient-to-r from-blue-50 to-purple-50">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              Why Choose Us?
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Our core values drive everything we do and set us apart in the
              industry
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <div
                key={index}
                className="card p-8 text-center group hover:shadow-2xl transition-all duration-300"
              >
                <div className="w-16 h-16 mx-auto mb-6 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center text-2xl transform group-hover:scale-110 transition-transform duration-300">
                  {value.icon}
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-4 group-hover:text-blue-600 transition-colors">
                  {value.title}
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  {value.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      <section className="py-20 bg-white">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              Our Core Values
            </h2>
            <p className="text-xl text-gray-600">
              What drives us to deliver excellence every day
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="card p-8 text-center group hover:shadow-2xl transition-all duration-300">
              <div className="w-16 h-16 mx-auto mb-6 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center text-2xl transform group-hover:scale-110 transition-transform duration-300">
                ⭐
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">Quality</h3>
              <p className="text-gray-600">
                Always put service quality as the top core priority
              </p>
            </div>

            <div className="card p-8 text-center group hover:shadow-2xl transition-all duration-300">
              <div className="w-16 h-16 mx-auto mb-6 rounded-full bg-gradient-to-r from-purple-500 to-pink-600 flex items-center justify-center text-2xl transform group-hover:scale-110 transition-transform duration-300">
                🔍
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">
                Conscientious
              </h3>
              <p className="text-gray-600">
                Always work based on customer benefits and satisfaction
              </p>
            </div>

            <div className="card p-8 text-center group hover:shadow-2xl transition-all duration-300">
              <div className="w-16 h-16 mx-auto mb-6 rounded-full bg-gradient-to-r from-pink-500 to-red-600 flex items-center justify-center text-2xl transform group-hover:scale-110 transition-transform duration-300">
                ⚡
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">24/7</h3>
              <p className="text-gray-600">
                Always there when customers need us, no matter what time
              </p>
            </div>

            <div className="card p-8 text-center group hover:shadow-2xl transition-all duration-300">
              <div className="w-16 h-16 mx-auto mb-6 rounded-full bg-gradient-to-r from-green-500 to-blue-600 flex items-center justify-center text-2xl transform group-hover:scale-110 transition-transform duration-300">
                🎯
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">
                Specialization
              </h3>
              <p className="text-gray-600">
                Expert team with deep Facebook ads knowledge and experience
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              Meet Our{" "}
              <span className="bg-gradient-to-r from-red-500 to-orange-500 bg-clip-text text-transparent font-bold">
                Expert Team
              </span>
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Passionate professionals dedicated to your advertising success
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {teamMembers.map((member) => (
              <div
                key={member.id}
                className="flex flex-col items-center p-4 bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 group"
              >
                <div className="relative w-20 h-20 mb-3 rounded-full overflow-hidden border-3 border-blue-500 shadow-lg mx-auto">
                  <Image
                    src={member.image}
                    alt="Team member"
                    fill
                    className="object-cover group-hover:scale-110 transition-transform duration-300"
                  />
                </div>
                <p className="text-gray-600 text-xs leading-relaxed text-center mb-3 line-clamp-2">
                  {member.description}
                </p>
                <a
                  href={member.linkedin}
                  className="inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors text-xs"
                >
                  <svg
                    className="w-3 h-3 mr-1"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M16.338 16.338H13.67V12.16c0-.995-.017-2.277-1.387-2.277-1.39 0-1.601 1.086-1.601 2.207v4.248H8.014v-8.59h2.559v1.174h.037c.356-.675 1.227-1.387 2.526-1.387 2.703 0 3.203 1.778 3.203 4.092v4.711zM5.005 6.575a1.548 1.548 0 11-.003-3.096 1.548 1.548 0 01.003 3.096zm-1.337 9.763H6.34v-8.59H3.667v8.59zM17.668 1H2.328C1.595 1 1 1.581 1 2.298v15.403C1 18.418 1.595 19 2.328 19h15.34c.734 0 1.332-.582 1.332-1.299V2.298C19 1.581 18.402 1 17.668 1z"
                      clipRule="evenodd"
                    />
                  </svg>
                  LinkedIn
                </a>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-blue-600 to-purple-600">
        <div className="container mx-auto px-6 text-center">
          <div className="max-w-3xl mx-auto">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Ready to Get Started?
            </h2>
            <p className="text-xl text-blue-100 mb-8">
              Join hundreds of satisfied customers who trust Golden Agency for
              their Facebook advertising needs.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-white text-blue-600 font-semibold px-8 py-4 rounded-full hover:bg-gray-100 transition-colors">
                Contact Us Today
              </button>
              <button className="border-2 border-white text-white font-semibold px-8 py-4 rounded-full hover:bg-white hover:text-blue-600 transition-colors">
                View Our Services
              </button>
            </div>
          </div>
        </div>
      </section>

      <Contact />
    </div>
  );
}
