.CategoryBox_content__detail-project {
  width: 100%;
  display: inline-block;
  margin-top: 3.5rem;
  display: flex;
  cursor: pointer;
  flex-direction: column;
  align-items: center;
  padding-left: 0.938rem;
  padding-right: 0.938rem;
}
.CategoryBox_content__detail-project:hover .CategoryBox_logo__icon--fill {
  fill: rgb(239, 68, 68);
}

.CategoryBox_content__detail-project:hover .CategoryBox_logo__icon--stroke {
  stroke: rgb(239, 68, 68);
}
.CategoryBox_content__detail-project:hover .CategoryBox_detail__content__title {
  color: rgb(239, 68, 68);
}
.CategoryBox_content__detail-project:hover .CategoryBox_detail__logo {
  box-shadow: 0 4px 32px rgba(0, 0, 0, 0.1);
  border-style: none;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255);
}

.link-img-service {
  box-sizing: border-box;
  display: inline-block;
  overflow: hidden;
  width: initial;
  height: initial;
  background: none;
  opacity: 1;
  border: 0px;
  margin: 0px;
  padding: 0px;
  position: relative;
  max-width: 100%;
}

.css-sv7pro {
  width: 200%;
  height: 350px;
  order: 1;
}

.group-button-in-project {
}

@media (min-width: 480px) {
  .css-sv7pro {
    width: 150%;
  }
  .CategoryBox_content__detail-project {
    padding-left: 1.25rem;
    padding-right: 1.25rem;
  }
}

@media (min-width: 768px) {
  .css-sv7pro {
    width: 765px;
  }
  .CategoryBox_content__detail-project {
    padding-left: 0;
    padding-right: 0;
  }
}

@media (min-width: 976px) {
  .css-sv7pro {
    width: 847px;
    order: 2;
  }
  .CategoryBox_content__detail-project {
    padding-left: 0.875rem;
    padding-right: 0.875rem;
  }
  .group-button-in-project {
    display: flex;
    gap: 1rem;
    margin-top: 2.5rem;
    order: 2;
    z-index: 10;
  }
}

@media (max-width: 976px) {
  .group-button-in-project {
    display: flex;
    gap: 1rem;
    margin-top: 2.5rem;
    order: 2;
    z-index: 10;
  }
}

@media (min-width: 1110px) {
  .css-sv7pro {
    width: 880px;
  }
  .CategoryBox_content__detail-project {
    padding-left: 1.75rem;
    padding-right: 1.75rem;
  }
}
