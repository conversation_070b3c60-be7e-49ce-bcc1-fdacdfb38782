"use client";
import Contact from "@/app/components/contact";

export default function ContactPage() {
  const contactMethods = [
    {
      icon: "📧",
      title: "Email Us",
      description: "Send us an email and we'll respond within 24 hours",
      contact: "<EMAIL>",
      action: "mailto:<EMAIL>",
    },
    {
      icon: "💬",
      title: "Telegram",
      description: "Chat with us instantly on Telegram",
      contact: "@GoldenAgencySupport",
      action: "https://t.me/GoldenAgencySupport",
    },
    {
      icon: "📱",
      title: "WhatsApp",
      description: "Message us on WhatsApp for quick support",
      contact: "+****************",
      action: "https://wa.me/***********",
    },
    {
      icon: "🕒",
      title: "24/7 Support",
      description: "We're available around the clock to help you",
      contact: "Always Online",
      action: "#",
    },
  ];

  const faqs = [
    {
      question: "How quickly can I get started?",
      answer:
        "After our initial consultation, we can have your account set up and ready for testing within 24-48 hours.",
    },
    {
      question: "Is there a free trial period?",
      answer:
        "Yes! We offer a 7-day free trial period so you can test the account performance before making any commitments.",
    },
    {
      question: "What payment methods do you accept?",
      answer:
        "We accept various payment methods including bank transfers, PayPal, cryptocurrency, and other secure payment options.",
    },
    {
      question: "Do you provide 24/7 support?",
      answer:
        "Absolutely! Our support team is available 24/7 via Telegram, WhatsApp, and email to assist you with any questions or issues.",
    },
  ];

  return (
    <div className="overflow-x-hidden">
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
        <div className="container mx-auto px-6 text-center">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-4xl md:text-6xl font-bold text-white mb-6 leading-tight">
              Get In{" "}
              <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                Touch
              </span>
            </h1>
            <p className="text-xl md:text-2xl text-gray-200 mb-8 max-w-3xl mx-auto">
              Ready to take your Facebook advertising to the next level? Contact
              our expert team today for a free consultation.
            </p>
          </div>
        </div>
      </section>

      {/* Contact Methods */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              Multiple Ways to{" "}
              <span className="bg-gradient-to-r from-red-500 to-orange-500 bg-clip-text text-transparent font-bold">
                Connect
              </span>
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Choose your preferred method to get in touch with our team
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {contactMethods.map((method, index) => (
              <a
                key={index}
                href={method.action}
                className="card p-6 text-center group hover:shadow-2xl transition-all duration-300 block"
              >
                <div className="w-16 h-16 mx-auto mb-6 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center text-2xl transform group-hover:scale-110 transition-transform duration-300">
                  {method.icon}
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors">
                  {method.title}
                </h3>
                <p className="text-gray-600 text-sm mb-4 leading-relaxed">
                  {method.description}
                </p>
                <p className="text-blue-600 font-semibold">{method.contact}</p>
              </a>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              Frequently Asked{" "}
              <span className="bg-gradient-to-r from-red-500 to-orange-500 bg-clip-text text-transparent font-bold">
                Questions
              </span>
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Quick answers to common questions about our services
            </p>
          </div>

          <div className="max-w-3xl mx-auto space-y-6">
            {faqs.map((faq, index) => (
              <div key={index} className="card p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-3">
                  {faq.question}
                </h3>
                <p className="text-gray-600 leading-relaxed">{faq.answer}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      <Contact />
    </div>
  );
}
