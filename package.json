{"name": "mercury-project", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "postinstall": "flowbite-react patch"}, "dependencies": {"@emailjs/browser": "^4.4.1", "@tailwindcss/line-clamp": "^0.4.4", "@types/react-slick": "^0.23.13", "clsx": "^2.1.1", "flowbite": "^3.1.2", "flowbite-react": "^0.11.5", "next": "15.1.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-slick": "^0.30.3", "slick-carousel": "^1.8.1", "swiper": "^11.2.10"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.4", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}